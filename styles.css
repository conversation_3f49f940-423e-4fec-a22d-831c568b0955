@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;600;700&family=Poppins:wght@300;400;600&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    overflow-x: hidden;
    background: linear-gradient(-45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3, #54a0ff);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

.birthday-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    padding: 60px 40px;
    text-align: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1),
                inset 0 -10px 20px rgba(255, 255, 255, 0.1),
                inset 0 10px 20px rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    max-width: 800px;
    width: 95%;
    transform: scale(0);
    animation: cardPop 1s ease-out 0.5s forwards;
    position: relative;
    overflow: hidden;
}

@keyframes cardPop {
    0% { transform: scale(0) rotate(-180deg); opacity: 0; }
    50% { transform: scale(1.1) rotate(-5deg); }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

.main-title {
    font-family: 'Dancing Script', cursive;
    font-size: clamp(2.5rem, 8vw, 4.5rem);
    font-weight: 700;
    color: #fff;
    text-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    margin-bottom: 20px;
    opacity: 0;
    animation: titleSlide 1s ease-out 1.5s forwards;
}

@keyframes titleSlide {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.subtitle {
    font-size: 1.3rem;
    color: #fff;
    margin-bottom: 30px;
    opacity: 0;
    animation: fadeInUp 1s ease-out 2s forwards;
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.message {
    font-size: 1.1rem;
    color: #fff;
    line-height: 1.6;
    margin-bottom: 40px;
    opacity: 0;
    animation: fadeInUp 1s ease-out 2.5s forwards;
}

.celebration-btn {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    border: none;
    padding: 15px 40px;
    border-radius: 50px;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transform: translateY(50px);
    opacity: 0;
    animation: buttonRise 1s ease-out 3s forwards;
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
}

.celebration-btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 20px 40px rgba(255, 107, 107, 0.4);
}

@keyframes buttonRise {
    from { opacity: 0; transform: translateY(50px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Floating hearts */
.heart {
    position: absolute;
    color: #ff6b6b;
    font-size: 20px;
    animation: floatHeart 6s linear infinite;
    opacity: 0;
}

@keyframes floatHeart {
    0% {
        opacity: 1;
        transform: translateY(100vh) scale(0);
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(-10vh) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-20vh) scale(0);
    }
}

/* Confetti */
.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #feca57;
    animation: confettiFall 3s linear infinite;
}

@keyframes confettiFall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
    }
}

.celebration-active {
    animation: celebrate 2s ease-in-out infinite;
}

@keyframes celebrate {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.05) rotate(-2deg); }
    75% { transform: scale(1.05) rotate(2deg); }
}

/* Stars */
.star {
    position: absolute;
    color: #fff;
    font-size: 16px;
    animation: twinkle 2s ease-in-out infinite alternate;
}

@keyframes twinkle {
    from { opacity: 0.3; transform: scale(1); }
    to { opacity: 1; transform: scale(1.2); }
}

/* Gift box animation */
.gift-container {
    margin: 30px 0;
    opacity: 0;
    animation: fadeInUp 1s ease-out 3.5s forwards;
}

.gift-box {
    font-size: 4rem;
    cursor: pointer;
    transition: transform 0.3s ease;
    animation: giftBounce 2s ease-in-out infinite;
}

.gift-box:hover {
    transform: scale(1.1);
}

@keyframes giftBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Birthday cake */
.cake {
    font-size: 3rem;
    margin: 20px 0;
    animation: cakeGlow 3s ease-in-out infinite alternate;
}

@keyframes cakeGlow {
    from { filter: drop-shadow(0 0 5px #feca57); }
    to { filter: drop-shadow(0 0 20px #feca57); }
}

.hidden-message {
    display: none;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
    animation: messageReveal 1s ease-out;
}

@keyframes messageReveal {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

/* Photo Gallery Styles */
.photo-gallery {
    margin-top: 20px;
}

.photo-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 20px;
    max-width: 800px;
    margin: 0 auto 20px;
}

.photo-slot {
    position: relative;
    aspect-ratio: 16/9;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.photo-slot:hover {
    transform: scale(1.05) rotate(2deg);
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.photo-display {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px;
    transition: all 0.3s ease;
    animation: photoReveal 0.8s ease-out;
}

.photo-display:hover {
    transform: scale(1.05);
    filter: brightness(1.1) saturate(1.2);
}

/* Featured Photo Styles */
.featured-photo-container {
    text-align: center;
}

.featured-photo-slot {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s ease;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 3px solid rgba(255, 255, 255, 0.4);
    animation: featuredGlow 3s ease-in-out infinite alternate;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.featured-photo-slot:hover {
    transform: scale(1.1) rotate(5deg);
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

@keyframes featuredGlow {
    from { box-shadow: 0 0 20px rgba(254, 202, 87, 0.3); }
    to { box-shadow: 0 0 40px rgba(255, 107, 107, 0.5); }
}

.featured-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: all 0.4s ease;
    animation: photoFloat 4s ease-in-out infinite;
}

.featured-photo:hover {
    transform: scale(1.05);
    filter: brightness(1.2) saturate(1.2);
}

@keyframes photoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Photo loading animation */
@keyframes photoReveal {
    0% {
        opacity: 0;
        transform: scale(0.5) rotate(180deg);
        filter: blur(10px);
    }
    50% {
        transform: scale(1.1) rotate(0deg);
        filter: blur(0px);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
        filter: blur(0px);
    }
}

@media (max-width: 768px) {
    .birthday-card {
        padding: 30px 20px;
        margin: 15px;
    }
    
    .main-title {
        font-size: clamp(2rem, 6vw, 2.5rem);
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .message {
        font-size: 0.95rem;
        line-height: 1.5;
    }
    
    .photo-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
    
    .media-slot {
        aspect-ratio: 16/9;
    }

    .media-display.video-display {
        max-height: 160px;
        width: 100%;
        object-fit: cover;
    }
    
    .featured-photo-slot, .featured-media-slot {
        width: 120px;
        height: 120px;
    }
}

@media (max-width: 480px) {
    .birthday-card {
        padding: 20px 15px;
    }
    
    .photo-grid {
        grid-template-columns: 1fr;
        max-width: 300px;
        margin: 0 auto 15px;
    }
    
    .gift-box {
        font-size: 3rem;
    }
    
    .cake {
        font-size: 2.5rem;
    }
    
    .celebration-btn {
        padding: 12px 30px;
        font-size: 1rem;
    }
}