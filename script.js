// Create floating hearts
function createHeart() {
    const heart = document.createElement('div');
    heart.className = 'heart';
    heart.innerHTML = '💖';
    heart.style.left = Math.random() * 100 + '%';
    heart.style.animationDelay = Math.random() * 2 + 's';
    heart.style.fontSize = (Math.random() * 20 + 15) + 'px';
    document.body.appendChild(heart);
    
    setTimeout(() => {
        heart.remove();
    }, 6000);
}

// Create confetti
function createConfetti() {
    const colors = ['#ff6b6b', '#feca57', '#48dbfb', '#ff9ff3', '#54a0ff'];
    const confetti = document.createElement('div');
    confetti.className = 'confetti';
    confetti.style.left = Math.random() * 100 + '%';
    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
    confetti.style.animationDelay = Math.random() * 3 + 's';
    confetti.style.borderRadius = Math.random() > 0.5 ? '50%' : '0';
    document.body.appendChild(confetti);
    
    setTimeout(() => {
        confetti.remove();
    }, 3000);
}

// Create stars
function createStars() {
    const stars = ['✨', '⭐', '🌟', '💫'];
    for (let i = 0; i < 20; i++) {
        const star = document.createElement('div');
        star.className = 'star';
        star.innerHTML = stars[Math.floor(Math.random() * stars.length)];
        star.style.left = Math.random() * 100 + '%';
        star.style.top = Math.random() * 100 + '%';
        star.style.animationDelay = Math.random() * 2 + 's';
        document.body.appendChild(star);
    }
}

// Start celebration
function startCelebration() {
    const card = document.querySelector('.birthday-card');
    card.classList.add('celebration-active');
    
    // Create lots of confetti
    for (let i = 0; i < 50; i++) {
        setTimeout(() => createConfetti(), i * 100);
    }
    
    // Create hearts
    for (let i = 0; i < 20; i++) {
        setTimeout(() => createHeart(), i * 200);
    }
    
    // Play celebration sound (visual feedback)
    const button = document.querySelector('.celebration-btn');
    button.innerHTML = '🎊 AMAZING! 🎊';
    button.style.background = 'linear-gradient(45deg, #48dbfb, #ff9ff3)';
    
    setTimeout(() => {
        button.innerHTML = '🎉 Celebrate Again! 🎉';
        button.style.background = 'linear-gradient(45deg, #ff6b6b, #feca57)';
        card.classList.remove('celebration-active');
    }, 2000);
}

// Open gift
function openGift() {
    const giftBox = document.querySelector('.gift-box');
    const hiddenMessage = document.getElementById('hiddenMessage');
    
    giftBox.style.transform = 'scale(1.2) rotate(360deg)';
    giftBox.innerHTML = '🎊';
    
    setTimeout(() => {
        hiddenMessage.style.display = 'block';
        hiddenMessage.scrollIntoView({ behavior: 'smooth' });
        
        // Create celebration effects
        for (let i = 0; i < 30; i++) {
            setTimeout(() => createConfetti(), i * 50);
            setTimeout(() => createHeart(), i * 100);
        }
    }, 1000);
}

// Initialize
window.addEventListener('load', () => {
    createStars();
    
    // Continuous heart creation
    setInterval(createHeart, 3000);
    
    // Random confetti
    setInterval(() => {
        if (Math.random() > 0.7) {
            createConfetti();
        }
    }, 5000);
});

// Add click sparkles
document.addEventListener('click', (e) => {
    const sparkle = document.createElement('div');
    sparkle.innerHTML = '✨';
    sparkle.style.position = 'absolute';
    sparkle.style.left = e.clientX + 'px';
    sparkle.style.top = e.clientY + 'px';
    sparkle.style.pointerEvents = 'none';
    sparkle.style.fontSize = '20px';
    sparkle.style.animation = 'fadeInUp 1s ease-out forwards';
    document.body.appendChild(sparkle);
    
    setTimeout(() => sparkle.remove(), 1000);
});